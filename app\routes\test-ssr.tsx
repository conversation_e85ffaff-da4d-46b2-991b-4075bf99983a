import { useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Table,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  Slider,
  Rate,
  Progress,
  Tag,
  Alert,
  Spin,
  Tooltip,
  Popconfirm,
  Drawer,
  Tabs,
  Collapse,
  Badge,
  Avatar,
  Space,
  Divider,
  Typography,
  notification,
  UserOutlined,
  SettingOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from "~/utils/antd";
import type { MetaFunction } from "@netlify/remix-runtime";
import { useSSRSafe, useWindowSize } from "~/hooks/useSSRSafe";

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

export const meta: MetaFunction = () => {
  return [
    { title: "SSR Test - Ant Design Components" },
    { name: "description", content: "Testing Ant Design SSR compatibility" },
  ];
};

export default function TestSSRPage() {
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const isClient = useSSRSafe();
  const windowSize = useWindowSize();

  // Sample data for table
  const dataSource = [
    {
      key: "1",
      name: "John Brown",
      age: 32,
      address: "New York No. 1 Lake Park",
      status: "active",
    },
    {
      key: "2",
      name: "Jim Green",
      age: 42,
      address: "London No. 1 Lake Park",
      status: "inactive",
    },
  ];

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: "Age",
      dataIndex: "age",
      key: "age",
    },
    {
      title: "Address",
      dataIndex: "address",
      key: "address",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Tag color={status === "active" ? "green" : "red"}>{status}</Tag>
      ),
    },
    {
      title: "Action",
      key: "action",
      render: () => (
        <Space size="middle">
          <Tooltip title="Edit">
            <Button type="link" icon={<EditOutlined />} />
          </Tooltip>
          <Popconfirm title="Are you sure?" onConfirm={() => {}}>
            <Button type="link" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleNotification = () => {
    notification.success({
      message: "Success",
      description: "This is a success notification from SSR test page!",
    });
  };

  const handleAsyncAction = async () => {
    setLoading(true);
    // Simulate async operation
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setLoading(false);
    handleNotification();
  };

  return (
    <div style={{ padding: "24px" }}>
      <Title level={2}>Ant Design SSR Compatibility Test</Title>
      
      <Alert
        message="SSR Status"
        description={`Client-side hydrated: ${isClient ? "Yes" : "No"} | Window size: ${windowSize.width}x${windowSize.height}`}
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* Basic Components */}
        <Card title="Basic Components" extra={<Badge count={5} />}>
          <Space wrap>
            <Button type="primary" icon={<PlusOutlined />}>
              Primary Button
            </Button>
            <Button type="default">Default Button</Button>
            <Button type="dashed">Dashed Button</Button>
            <Button type="link">Link Button</Button>
            <Button danger>Danger Button</Button>
            <Button loading={loading} onClick={handleAsyncAction}>
              {loading ? "Loading..." : "Test Async"}
            </Button>
          </Space>
        </Card>

        {/* Form Components */}
        <Card title="Form Components">
          <Form form={form} layout="vertical">
            <Form.Item label="Input" name="input">
              <Input placeholder="Enter text" />
            </Form.Item>
            <Form.Item label="Select" name="select">
              <Select placeholder="Select option">
                <Option value="option1">Option 1</Option>
                <Option value="option2">Option 2</Option>
              </Select>
            </Form.Item>
            <Form.Item label="Date Picker" name="date">
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item label="Switch" name="switch" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Form>
        </Card>

        {/* Interactive Components */}
        <Card title="Interactive Components">
          <Space direction="vertical" style={{ width: "100%" }}>
            <div>
              <Text>Slider: </Text>
              <Slider defaultValue={30} style={{ width: 200 }} />
            </div>
            <div>
              <Text>Rate: </Text>
              <Rate defaultValue={3} />
            </div>
            <div>
              <Text>Progress: </Text>
              <Progress percent={70} />
            </div>
          </Space>
        </Card>

        {/* Data Display */}
        <Card title="Data Display">
          <Table dataSource={dataSource} columns={columns} pagination={false} />
        </Card>

        {/* Tabs and Collapse */}
        <Card title="Layout Components">
          <Tabs defaultActiveKey="1">
            <TabPane tab="Tab 1" key="1">
              <Paragraph>Content of Tab Pane 1</Paragraph>
            </TabPane>
            <TabPane tab="Tab 2" key="2">
              <Paragraph>Content of Tab Pane 2</Paragraph>
            </TabPane>
          </Tabs>
          
          <Divider />
          
          <Collapse defaultActiveKey={["1"]}>
            <Panel header="Panel 1" key="1">
              <Paragraph>Content of panel 1</Paragraph>
            </Panel>
            <Panel header="Panel 2" key="2">
              <Paragraph>Content of panel 2</Paragraph>
            </Panel>
          </Collapse>
        </Card>

        {/* Modal and Drawer Triggers */}
        <Card title="Overlay Components">
          <Space>
            <Button onClick={() => setModalVisible(true)}>Open Modal</Button>
            <Button onClick={() => setDrawerVisible(true)}>Open Drawer</Button>
            <Button onClick={handleNotification}>Show Notification</Button>
          </Space>
        </Card>

        {/* Loading States */}
        <Card title="Loading States">
          <Space>
            <Spin size="small" />
            <Spin />
            <Spin size="large" />
            <Spin spinning={loading}>
              <div style={{ padding: 20, background: "#f5f5f5" }}>
                Content that can be in loading state
              </div>
            </Spin>
          </Space>
        </Card>
      </Space>

      {/* Modal */}
      <Modal
        title="Test Modal"
        open={modalVisible}
        onOk={() => setModalVisible(false)}
        onCancel={() => setModalVisible(false)}
      >
        <Paragraph>This modal should render correctly in SSR environment.</Paragraph>
        <Form layout="vertical">
          <Form.Item label="Modal Input">
            <Input placeholder="Enter something" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Drawer */}
      <Drawer
        title="Test Drawer"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        <Paragraph>This drawer should render correctly in SSR environment.</Paragraph>
        <Space direction="vertical">
          <Avatar size={64} icon={<UserOutlined />} />
          <Button type="primary" block>
            Action Button
          </Button>
        </Space>
      </Drawer>
    </div>
  );
}
