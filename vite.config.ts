import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { netlifyPlugin } from "@netlify/remix-edge-adapter/plugin";

export default defineConfig({
  plugins: [remix(), netlifyPlugin(), tsconfigPaths()],
  build: {
    // Enable tree shaking
    rollupOptions: {
      output: {
        // Manual chunks for better code splitting
        manualChunks: {
          // Separate vendor chunks for better caching
          'antd-core': ['antd'],
          'antd-icons': ['@ant-design/icons'],
          'antd-cssinjs': ['@ant-design/cssinjs'],
        },
      },
    },
  },
  optimizeDeps: {
    // Pre-bundle these dependencies for better performance
    include: [
      'antd',
      '@ant-design/icons',
      '@ant-design/cssinjs',
      'react',
      'react-dom',
      'dayjs',
    ],
  },
  ssr: {
    // Optimize SSR dependencies
    noExternal: ['antd', '@ant-design/icons', '@ant-design/cssinjs'],
  },
});
