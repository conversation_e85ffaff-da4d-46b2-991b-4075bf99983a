import { useEffect, useState } from "react";

/**
 * Hook to safely handle client-side only operations in SSR environment
 * This prevents hydration mismatches for components that behave differently on server vs client
 */
export function useSSRSafe() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely get window dimensions in SSR environment
 * Returns default values on server and actual values on client
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: 1200, // Default width for SSR
    height: 800, // Default height for SSR
  });

  const isClient = useSSRSafe();

  useEffect(() => {
    if (!isClient) return;

    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // Set initial size
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isClient]);

  return windowSize;
}

/**
 * Hook to safely access localStorage in SSR environment
 */
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(defaultValue);
  const isClient = useSSRSafe();

  useEffect(() => {
    if (!isClient) return;

    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        setValue(JSON.parse(item));
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
    }
  }, [key, isClient]);

  const setStoredValue = (newValue: T) => {
    try {
      setValue(newValue);
      if (isClient) {
        window.localStorage.setItem(key, JSON.stringify(newValue));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [value, setStoredValue] as const;
}
