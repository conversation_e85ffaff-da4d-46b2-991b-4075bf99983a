import {
  <PERSON>s,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLocation,
  useNavigate,
} from "@remix-run/react";
import { Layout, Menu, type MenuProps } from "~/utils/antd";
import { useState } from "react";
import { Logo } from "./components/Logo/Logo";

import stylesheet from "~/tailwind.css?url";
import { LinksFunction } from "@remix-run/node";
import { UserIcon } from "lucide-react";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: stylesheet },
];

const { Content, Sider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

const items: MenuItem[] = [
  {
    label: "Menu",
    type: "group",
    children: [
      {
        label: "Platforms",
        key: "/platforms",
        icon: <UserIcon />,
      },
      {
        label: "Sections",
        key: "/sections",
        icon: <UserIcon />,
      },
      {
        label: "SSR Test",
        key: "/test-ssr",
        icon: <UserIcon />,
      },
    ],
  },
];

export default function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <Layout style={{ minHeight: "100vh" }}>
          <Sider
            collapsible
            collapsed={collapsed}
            onCollapse={(value) => setCollapsed(value)}
          >
            <div className="p-3 border-b border-gray-300">
              <Logo variant="horizontal" size="md" />
            </div>
            <Menu
              theme="dark"
              defaultSelectedKeys={[location.pathname]}
              mode="inline"
              items={items}
              onClick={(e) => {
                navigate(e.key);
              }}
              selectedKeys={[location.pathname]}
            />
          </Sider>
          <Layout>
            <Content style={{ margin: "0 16px" }}>
              <div
                style={{
                  padding: 24,
                  minHeight: 360,
                }}
              >
                <Outlet />
              </div>
            </Content>
          </Layout>
        </Layout>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}
