import { Remix<PERSON>rowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { createCache, StyleProvider } from "@ant-design/cssinjs";
import { AntdProvider } from "./components/AntdProvider";

// Create a cache for CSS-in-JS styles on the client
const cache = createCache();

// Function to handle style hydration
function hydrateStyles() {
  // Extract existing styles from the server-rendered HTML
  const existingStyles = document.querySelector(
    'style[data-ant-cssinjs-cache-path="_ant_cssinjs_cache_path_"]'
  );

  if (existingStyles) {
    // Mark the style element to prevent removal during hydration
    existingStyles.setAttribute('data-ant-cssinjs-hydrated', 'true');

    // Extract the CSS content and populate the cache
    const cssText = existingStyles.innerHTML;
    if (cssText) {
      // Store the styles in the cache to prevent duplication
      cache.cache.set("_ant_cssinjs_cache_path_", cssText);
    }
  }
}

// Hydrate styles before React hydration
hydrateStyles();

startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <StyleProvider cache={cache}>
        <AntdProvider>
          <RemixBrowser />
        </AntdProvider>
      </StyleProvider>
    </StrictMode>
  );
});
