import { ConfigProvider, theme, App } from "~/utils/antd";
import { ReactNode, useEffect } from "react";

interface AntdProviderProps {
  children: ReactNode;
}

export function AntdProvider({ children }: AntdProviderProps) {
  // Clean up any duplicate styles on the client side
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Remove any duplicate style tags that might have been created
      const duplicateStyles = document.querySelectorAll(
        'style[data-ant-cssinjs-cache-path="_ant_cssinjs_cache_path_"]:not([data-ant-cssinjs-hydrated])'
      );
      duplicateStyles.forEach((style) => style.remove());
    }
  }, []);

  return (
    <ConfigProvider
      theme={{
        // Use the default algorithm for consistent theming
        algorithm: theme.defaultAlgorithm,
        // Configure token overrides for custom theming
        token: {
          // Primary color that matches your brand
          colorPrimary: "#2563eb", // Blue color used in the project
          // Border radius for consistent styling
          borderRadius: 6,
          // Font family
          fontFamily: "system-ui, -apple-system, sans-serif",
          // Ensure consistent font sizes
          fontSize: 14,
          // Line height for better readability
          lineHeight: 1.5715,
        },
        // Component-specific configurations
        components: {
          Layout: {
            // Ensure layout components work well with SSR
            bodyBg: "#ffffff",
            siderBg: "#001529", // Dark sidebar background
            headerBg: "#ffffff",
          },
          Menu: {
            // Menu styling for dark theme sidebar
            darkItemBg: "#001529",
            darkSubMenuItemBg: "#000c17",
            darkItemSelectedBg: "#1890ff",
            darkItemHoverBg: "#112545",
          },
          Button: {
            // Button styling
            borderRadius: 6,
            controlHeight: 32,
          },
          Table: {
            // Table styling
            borderRadius: 6,
            headerBg: "#fafafa",
          },
          Modal: {
            // Modal styling for better SSR compatibility
            borderRadius: 8,
          },
          Card: {
            // Card styling
            borderRadius: 8,
          },
        },
      }}
      // Ensure consistent locale and direction for SSR
      direction="ltr"
      // Disable motion for better SSR performance
      motion={false}
    >
      <App>
        {children}
      </App>
    </ConfigProvider>
  );
}
