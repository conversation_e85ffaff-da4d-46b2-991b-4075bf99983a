import type { EntryContext } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import { renderToString } from "react-dom/server";
import { createCache, extractStyle, StyleProvider } from "@ant-design/cssinjs";
import { AntdProvider } from "./components/AntdProvider";

export default function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext
) {
  // Create a cache for CSS-in-JS styles with a unique key
  const cache = createCache();

  // Render the app with both StyleProvider and AntdProvider
  const markup = renderToString(
    <StyleProvider cache={cache}>
      <AntdProvider>
        <RemixServer context={remixContext} url={request.url} />
      </AntdProvider>
    </StyleProvider>
  );

  // Extract styles from the cache
  const styles = extractStyle(cache, true);

  // Create a more robust style injection that handles existing head content
  let html = markup;

  if (styles) {
    // Inject styles before the closing head tag
    html = html.replace(
      /<\/head>/i,
      `<style data-ant-cssinjs-cache-path="_ant_cssinjs_cache_path_" data-rc-order="prependQueue,queue" data-rc-priority="0">${styles}</style></head>`
    );
  }

  responseHeaders.set("Content-Type", "text/html");

  return new Response(`<!DOCTYPE html>${html}`, {
    status: responseStatusCode,
    headers: responseHeaders,
  });
}
