# Ant Design SSR Configuration Guide

This guide explains how Ant Design has been configured for Server-Side Rendering (SSR) compatibility in this Remix project.

## Overview

Ant Design 5.x uses CSS-in-JS by default, which can cause hydration mismatches between server and client rendering. This configuration ensures consistent styling and prevents common SSR issues.

## Configuration Files

### 1. Entry Server (`app/entry.server.tsx`)

The server entry point handles CSS-in-JS style extraction during SSR:

```typescript
import { createCache, extractStyle, StyleProvider } from "@ant-design/cssinjs";
import { AntdProvider } from "./components/AntdProvider";

// Creates a cache for CSS-in-JS styles
const cache = createCache();

// Renders with both StyleProvider and AntdProvider
const markup = renderToString(
  <StyleProvider cache={cache}>
    <AntdProvider>
      <RemixServer context={remixContext} url={request.url} />
    </AntdProvider>
  </StyleProvider>
);

// Extracts and injects styles into HTML
const styles = extractStyle(cache, true);
```

### 2. Entry Client (`app/entry.client.tsx`)

The client entry point handles style hydration:

```typescript
import { createCache, StyleProvider } from "@ant-design/cssinjs";

// Creates client-side cache
const cache = createCache();

// Handles style hydration from server-rendered HTML
function hydrateStyles() {
  const existingStyles = document.querySelector(
    'style[data-ant-cssinjs-cache-path="_ant_cssinjs_cache_path_"]'
  );
  
  if (existingStyles) {
    existingStyles.setAttribute('data-ant-cssinjs-hydrated', 'true');
    cache.cache.set("_ant_cssinjs_cache_path_", existingStyles.innerHTML);
  }
}
```

### 3. Ant Design Provider (`app/components/AntdProvider.tsx`)

Centralized configuration for Ant Design components:

```typescript
<ConfigProvider
  theme={{
    algorithm: theme.defaultAlgorithm,
    token: {
      colorPrimary: "#2563eb",
      borderRadius: 6,
      fontFamily: "system-ui, -apple-system, sans-serif",
    },
  }}
  direction="ltr"
  motion={false} // Disabled for better SSR performance
>
  <App>{children}</App>
</ConfigProvider>
```

### 4. Vite Configuration (`vite.config.ts`)

Optimized build configuration for tree shaking and bundle splitting:

```typescript
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'antd-core': ['antd'],
          'antd-icons': ['@ant-design/icons'],
          'antd-cssinjs': ['@ant-design/cssinjs'],
        },
      },
    },
  },
  ssr: {
    noExternal: ['antd', '@ant-design/icons', '@ant-design/cssinjs'],
  },
});
```

## Best Practices

### 1. Import Optimization

Use the centralized import utility (`app/utils/antd.ts`) for better tree shaking:

```typescript
// ✅ Good - Uses optimized imports
import { Button, Table, Modal } from "~/utils/antd";

// ❌ Avoid - Direct imports from antd
import { Button, Table, Modal } from "antd";
```

### 2. SSR-Safe Hooks

Use the provided SSR-safe hooks for client-only operations:

```typescript
import { useSSRSafe, useWindowSize, useLocalStorage } from "~/hooks/useSSRSafe";

function MyComponent() {
  const isClient = useSSRSafe();
  const { width, height } = useWindowSize();
  const [value, setValue] = useLocalStorage('key', 'defaultValue');
  
  // Only render client-specific content after hydration
  if (!isClient) {
    return <div>Loading...</div>;
  }
  
  return <div>Client content</div>;
}
```

### 3. Theme Consistency

All theme configuration is centralized in `AntdProvider`. Avoid inline theme overrides:

```typescript
// ✅ Good - Uses centralized theme
<Button type="primary">Submit</Button>

// ❌ Avoid - Inline theme overrides
<ConfigProvider theme={{...}}>
  <Button>Submit</Button>
</ConfigProvider>
```

## Common Issues and Solutions

### 1. Hydration Mismatch

**Problem**: Different styles between server and client rendering.

**Solution**: Ensure `StyleProvider` wraps your app on both server and client, and styles are properly extracted/hydrated.

### 2. Flash of Unstyled Content (FOUC)

**Problem**: Brief moment of unstyled content during page load.

**Solution**: The configuration automatically injects styles in the HTML head during SSR, preventing FOUC.

### 3. Large Bundle Size

**Problem**: Including unused Ant Design components increases bundle size.

**Solution**: Use the optimized import utility and configure manual chunks in Vite.

### 4. Motion/Animation Issues

**Problem**: Animations causing hydration mismatches.

**Solution**: Motion is disabled in the ConfigProvider for SSR compatibility.

## Testing SSR Configuration

Visit `/test-ssr` to see a comprehensive test page that demonstrates various Ant Design components working correctly with SSR.

## Performance Considerations

1. **Code Splitting**: Components are split into separate chunks for better caching
2. **Tree Shaking**: Only used components are included in the bundle
3. **Style Extraction**: CSS-in-JS styles are extracted during SSR for faster initial load
4. **Hydration Optimization**: Styles are properly hydrated to prevent re-rendering

## Troubleshooting

### Build Errors

If you encounter build errors:

1. Check that all Ant Design imports use the optimized utility
2. Ensure `@ant-design/cssinjs` is properly configured in Vite
3. Verify that SSR dependencies are marked as `noExternal`

### Runtime Errors

If you see hydration errors:

1. Check browser console for specific error messages
2. Ensure client and server render the same content
3. Verify that SSR-safe hooks are used for client-only operations

### Style Issues

If styles are not loading correctly:

1. Check that styles are being extracted on the server
2. Verify that the style cache is properly hydrated on the client
3. Ensure no conflicting CSS is overriding Ant Design styles

## Dependencies

- `antd`: ^5.26.3
- `@ant-design/cssinjs`: ^1.23.0
- `@ant-design/icons`: ^6.0.0

## Additional Resources

- [Ant Design SSR Documentation](https://ant.design/docs/react/server-side-rendering)
- [Remix SSR Guide](https://remix.run/docs/en/main/guides/server-side-rendering)
- [Vite SSR Configuration](https://vitejs.dev/guide/ssr.html)
